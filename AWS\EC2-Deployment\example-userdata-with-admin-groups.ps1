<powershell>
# Example EC2 User Data Script with Admin Groups Configuration
# This script demonstrates how to use the SLM_Config.json for admin group management

# Set execution policy
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process -Force

# Create log file
$logFile = "C:\Scripts\user-data.log"
if (!(Test-Path "C:\Scripts")) {
    New-Item -ItemType Directory -Path "C:\Scripts" -Force | Out-Null
}

function Write-Log {
    param([string]$Message)
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] $Message"
    Write-Host $logMessage
    Add-Content -Path $logFile -Value $logMessage
}

Write-Log "Starting EC2 User Data script execution..."

try {
    # Configuration parameters - these would typically come from EC2 tags or parameters
    $businessUnit = "SPF"  # or "SC"
    $environment = "PRD"   # or "DEV", "PPE"
    
    # Download configuration file from S3 or use local copy
    $configPath = "C:\Scripts\SLM_Config.json"
    
    # Example: Download from S3 (replace with your actual S3 bucket/path)
    # aws s3 cp s3://your-bucket/configs/SLM_Config.json $configPath
    
    # For this example, we'll create the config locally (in production, download from S3)
    $configUrl = "https://raw.githubusercontent.com/your-repo/configs/SLM_Config.json"
    
    Write-Log "Downloading configuration file..."
    try {
        # Download the configuration file
        Invoke-WebRequest -Uri $configUrl -OutFile $configPath -ErrorAction Stop
        Write-Log "Configuration file downloaded successfully"
    } catch {
        Write-Log "Failed to download config file: $($_.Exception.Message)"
        # Fallback: create minimal config for this example
        $fallbackConfig = @{
            business_units = @{
                SPF = @{
                    environments = @{
                        PRD = @{
                            DEFAULT_ADM = @(
                                "MUD\DL-Sanlamlife-TSMSLocalAdmin",
                                "MUD\DL-Sanlamlife-ADDMDisLocalAdmin",
                                "MUD\svcbmcadispaccount"
                            )
                        }
                        DEV = @{
                            DEFAULT_ADM = @("DG-SanlamLife-DEVLocalAdmin")
                        }
                        PPE = @{
                            DEFAULT_ADM = @("DG-SanlamLife-PPELocalAdmin")
                        }
                    }
                }
            }
        }
        $fallbackConfig | ConvertTo-Json -Depth 10 | Set-Content $configPath
        Write-Log "Using fallback configuration"
    }
    
    # Download and source the admin groups helper functions
    $helperScript = "C:\Scripts\Get-AdminGroups.ps1"
    
    # In production, download from S3 or your repository
    # For this example, we'll define the functions inline
    
    function Get-AdminGroups {
        param($ConfigPath, $BusinessUnit, $Environment)
        
        try {
            $config = Get-Content $ConfigPath | ConvertFrom-Json
            $adminGroups = $config.business_units.$BusinessUnit.environments.$Environment.DEFAULT_ADM
            
            if ($adminGroups) {
                Write-Log "Found admin groups for $BusinessUnit/$Environment: $($adminGroups -join ', ')"
                return $adminGroups
            } else {
                Write-Log "No admin groups found for $BusinessUnit/$Environment"
                return @()
            }
        } catch {
            Write-Log "Error reading admin groups: $($_.Exception.Message)"
            return @()
        }
    }
    
    function Add-AdminGroupsToLocal {
        param([string[]]$AdminGroups)
        
        $addedGroups = @()
        $failedGroups = @()
        
        foreach ($group in $AdminGroups) {
            try {
                # Check if group is already in administrators
                $currentAdmins = Get-LocalGroupMember -Group "Administrators" -ErrorAction SilentlyContinue | Select-Object -ExpandProperty Name
                
                if ($currentAdmins -contains $group) {
                    Write-Log "Group '$group' is already in Administrators group"
                    continue
                }
                
                # Add the group to local administrators
                Add-LocalGroupMember -Group "Administrators" -Member $group -ErrorAction Stop
                Write-Log "Successfully added '$group' to Administrators group"
                $addedGroups += $group
                
            } catch {
                Write-Log "Failed to add '$group' to Administrators group: $($_.Exception.Message)"
                $failedGroups += $group
            }
        }
        
        return @{
            Success = ($failedGroups.Count -eq 0)
            Added = $addedGroups
            Failed = $failedGroups
        }
    }
    
    # Get admin groups from configuration
    Write-Log "Retrieving admin groups for $businessUnit/$environment..."
    $adminGroups = Get-AdminGroups -ConfigPath $configPath -BusinessUnit $businessUnit -Environment $environment
    
    if ($adminGroups -and $adminGroups.Count -gt 0) {
        Write-Log "Configuring admin groups..."
        $result = Add-AdminGroupsToLocal -AdminGroups $adminGroups
        
        if ($result.Success) {
            Write-Log "Admin groups configured successfully. Added: $($result.Added -join ', ')"
        } else {
            Write-Log "Some admin groups failed to be added. Failed: $($result.Failed -join ', ')"
        }
    } else {
        Write-Log "No admin groups to configure"
    }
    
    # Continue with other user-data tasks...
    Write-Log "Continuing with other configuration tasks..."
    
    # Example: Domain join (if needed)
    # Add-Computer -DomainName "your.domain.com" -Credential $domainCreds -Restart
    
    # Example: Install software
    # Install-WindowsFeature -Name IIS-WebServerRole
    
    Write-Log "User data script completed successfully"
    
} catch {
    Write-Log "Error in user data script: $($_.Exception.Message)"
    Write-Log "Stack trace: $($_.ScriptStackTrace)"
}
</powershell>
