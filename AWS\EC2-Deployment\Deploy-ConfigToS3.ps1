# Deploy-ConfigToS3.ps1
# Script to deploy SLM_Config.json and helper scripts to S3 bucket

[CmdletBinding()]
param(
    [Parameter(Mandatory=$true)]
    [string]$S3Bucket,
    
    [Parameter(Mandatory=$false)]
    [string]$S3KeyPrefix = "configs",
    
    [Parameter(Mandatory=$false)]
    [string]$ConfigPath = "AWS\SystemsManagerAutomation\Configs\SLM_Config.json",
    
    [Parameter(Mandatory=$false)]
    [string]$Region = "us-east-1",
    
    [Parameter(Mandatory=$false)]
    [switch]$IncludeHelperScripts,
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun
)

function Write-DeployLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
}

function Test-S3Bucket {
    param([string]$BucketName, [string]$Region)
    
    try {
        $result = aws s3api head-bucket --bucket $BucketName --region $Region 2>&1
        if ($LASTEXITCODE -eq 0) {
            return $true
        } else {
            Write-DeployLog "S3 bucket '$BucketName' not accessible: $result" "ERROR"
            return $false
        }
    } catch {
        Write-DeployLog "Error checking S3 bucket: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Deploy-FileToS3 {
    param(
        [string]$LocalPath,
        [string]$S3Bucket,
        [string]$S3Key,
        [string]$Region,
        [switch]$DryRun
    )
    
    if (!(Test-Path $LocalPath)) {
        Write-DeployLog "Local file not found: $LocalPath" "ERROR"
        return $false
    }
    
    $s3Uri = "s3://$S3Bucket/$S3Key"
    
    if ($DryRun) {
        Write-DeployLog "DRY RUN: Would upload $LocalPath to $s3Uri" "INFO"
        return $true
    }
    
    try {
        Write-DeployLog "Uploading $LocalPath to $s3Uri..." "INFO"
        aws s3 cp $LocalPath $s3Uri --region $Region 2>&1 | Out-Null
        
        if ($LASTEXITCODE -eq 0) {
            Write-DeployLog "Successfully uploaded to $s3Uri" "INFO"
            return $true
        } else {
            Write-DeployLog "Failed to upload to $s3Uri" "ERROR"
            return $false
        }
    } catch {
        Write-DeployLog "Error uploading file: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Validate-ConfigFile {
    param([string]$ConfigPath)
    
    try {
        Write-DeployLog "Validating configuration file: $ConfigPath" "INFO"
        
        if (!(Test-Path $ConfigPath)) {
            Write-DeployLog "Configuration file not found: $ConfigPath" "ERROR"
            return $false
        }
        
        # Test JSON parsing
        $config = Get-Content $ConfigPath -Raw | ConvertFrom-Json
        
        # Validate structure
        if (!$config.business_units) {
            Write-DeployLog "Configuration missing 'business_units' section" "ERROR"
            return $false
        }
        
        $validationResults = @()
        
        foreach ($buName in $config.business_units.PSObject.Properties.Name) {
            $bu = $config.business_units.$buName
            
            if (!$bu.environments) {
                $validationResults += "Business unit '$buName' missing 'environments' section"
                continue
            }
            
            foreach ($envName in $bu.environments.PSObject.Properties.Name) {
                $env = $bu.environments.$envName
                
                if ($env.DEFAULT_ADM) {
                    if ($env.DEFAULT_ADM -is [array] -and $env.DEFAULT_ADM.Count -gt 0) {
                        Write-DeployLog "✓ $buName/$envName has $($env.DEFAULT_ADM.Count) admin groups defined" "INFO"
                    } else {
                        $validationResults += "$buName/$envName has empty DEFAULT_ADM array"
                    }
                } else {
                    $validationResults += "$buName/$envName missing DEFAULT_ADM configuration"
                }
            }
        }
        
        if ($validationResults.Count -gt 0) {
            Write-DeployLog "Configuration validation issues found:" "WARNING"
            foreach ($issue in $validationResults) {
                Write-DeployLog "  - $issue" "WARNING"
            }
        } else {
            Write-DeployLog "Configuration validation passed" "INFO"
        }
        
        return $true
        
    } catch {
        Write-DeployLog "Configuration validation failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

# Main execution
try {
    Write-DeployLog "Starting deployment to S3 bucket: $S3Bucket" "INFO"
    
    if ($DryRun) {
        Write-DeployLog "DRY RUN MODE - No files will be uploaded" "INFO"
    }
    
    # Check AWS CLI availability
    try {
        aws --version | Out-Null
        if ($LASTEXITCODE -ne 0) {
            throw "AWS CLI not available"
        }
    } catch {
        Write-DeployLog "AWS CLI is not available or not configured" "ERROR"
        exit 1
    }
    
    # Test S3 bucket access
    Write-DeployLog "Testing S3 bucket access..." "INFO"
    if (!(Test-S3Bucket -BucketName $S3Bucket -Region $Region)) {
        Write-DeployLog "Cannot access S3 bucket '$S3Bucket'" "ERROR"
        exit 1
    }
    
    # Validate configuration file
    if (!(Validate-ConfigFile -ConfigPath $ConfigPath)) {
        Write-DeployLog "Configuration validation failed" "ERROR"
        exit 1
    }
    
    # Deploy main configuration file
    $configS3Key = "$S3KeyPrefix/SLM_Config.json"
    $configDeployed = Deploy-FileToS3 -LocalPath $ConfigPath -S3Bucket $S3Bucket -S3Key $configS3Key -Region $Region -DryRun:$DryRun
    
    if (!$configDeployed) {
        Write-DeployLog "Failed to deploy main configuration file" "ERROR"
        exit 1
    }
    
    # Deploy helper scripts if requested
    if ($IncludeHelperScripts) {
        Write-DeployLog "Deploying helper scripts..." "INFO"
        
        $helperScripts = @(
            @{ Local = "AWS\EC2-Deployment\Get-AdminGroups.ps1"; S3Key = "scripts/Get-AdminGroups.ps1" },
            @{ Local = "AWS\EC2-Deployment\Validate-AdminGroups.ps1"; S3Key = "scripts/Validate-AdminGroups.ps1" }
        )
        
        $allHelpersDeployed = $true
        
        foreach ($script in $helperScripts) {
            $scriptS3Key = "$S3KeyPrefix/$($script.S3Key)"
            $scriptDeployed = Deploy-FileToS3 -LocalPath $script.Local -S3Bucket $S3Bucket -S3Key $scriptS3Key -Region $Region -DryRun:$DryRun
            
            if (!$scriptDeployed) {
                $allHelpersDeployed = $false
            }
        }
        
        if (!$allHelpersDeployed) {
            Write-DeployLog "Some helper scripts failed to deploy" "WARNING"
        }
    }
    
    # Summary
    Write-DeployLog "=== DEPLOYMENT SUMMARY ===" "INFO"
    Write-DeployLog "S3 Bucket: $S3Bucket" "INFO"
    Write-DeployLog "Region: $Region" "INFO"
    Write-DeployLog "Config S3 Key: $configS3Key" "INFO"
    
    if (!$DryRun) {
        Write-DeployLog "Configuration deployed successfully!" "INFO"
        Write-DeployLog "Your user-data scripts can now download from: s3://$S3Bucket/$configS3Key" "INFO"
    } else {
        Write-DeployLog "Dry run completed - no files were uploaded" "INFO"
    }
    
} catch {
    Write-DeployLog "Deployment failed: $($_.Exception.Message)" "ERROR"
    exit 1
}
