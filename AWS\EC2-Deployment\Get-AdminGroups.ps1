# Get-AdminGroups.ps1
# Helper function to retrieve default administrator groups from SLM_Config.json
# This function can be used in user-data scripts for EC2 deployment

function Get-AdminGroups {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$ConfigPath,
        
        [Parameter(Mandatory=$true)]
        [string]$BusinessUnit,
        
        [Parameter(Mandatory=$true)]
        [string]$Environment
    )
    
    try {
        # Load the configuration file
        if (!(Test-Path $ConfigPath)) {
            Write-Error "Configuration file not found: $ConfigPath"
            return $null
        }
        
        $config = Get-Content $ConfigPath | ConvertFrom-Json
        
        # Navigate to the specific business unit and environment
        $buConfig = $config.business_units.$BusinessUnit
        if (!$buConfig) {
            Write-Error "Business unit '$BusinessUnit' not found in configuration"
            return $null
        }
        
        $envConfig = $buConfig.environments.$Environment
        if (!$envConfig) {
            Write-Error "Environment '$Environment' not found for business unit '$BusinessUnit'"
            return $null
        }
        
        # Return the DEFAULT_ADM groups
        if ($envConfig.DEFAULT_ADM) {
            Write-Host "Found admin groups for $BusinessUnit/$Environment: $($envConfig.DEFAULT_ADM -join ', ')"
            return $envConfig.DEFAULT_ADM
        } else {
            Write-Warning "No DEFAULT_ADM groups defined for $BusinessUnit/$Environment"
            return @()
        }
        
    } catch {
        Write-Error "Error reading configuration: $($_.Exception.Message)"
        return $null
    }
}

function Add-AdminGroupsToLocal {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string[]]$AdminGroups,
        
        [Parameter(Mandatory=$false)]
        [string]$LogPath = "C:\Scripts\admin-groups.log"
    )
    
    try {
        # Ensure log directory exists
        $logDir = Split-Path $LogPath -Parent
        if (!(Test-Path $logDir)) {
            New-Item -ItemType Directory -Path $logDir -Force | Out-Null
        }
        
        # Function to write to log
        function Write-Log {
            param([string]$Message)
            $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            $logMessage = "[$timestamp] $Message"
            Write-Host $logMessage
            Add-Content -Path $LogPath -Value $logMessage
        }
        
        Write-Log "Starting admin group configuration..."
        
        # Get current members of local Administrators group
        $currentAdmins = @()
        try {
            $currentAdmins = Get-LocalGroupMember -Group "Administrators" | Select-Object -ExpandProperty Name
            Write-Log "Current administrators: $($currentAdmins -join ', ')"
        } catch {
            Write-Log "Warning: Could not retrieve current administrators: $($_.Exception.Message)"
        }
        
        # Add each admin group if not already present
        $addedGroups = @()
        $skippedGroups = @()
        $failedGroups = @()
        
        foreach ($group in $AdminGroups) {
            try {
                # Check if group is already in administrators
                if ($currentAdmins -contains $group) {
                    Write-Log "Group '$group' is already in Administrators group"
                    $skippedGroups += $group
                    continue
                }
                
                # Add the group to local administrators
                Add-LocalGroupMember -Group "Administrators" -Member $group -ErrorAction Stop
                Write-Log "Successfully added '$group' to Administrators group"
                $addedGroups += $group
                
            } catch {
                Write-Log "Failed to add '$group' to Administrators group: $($_.Exception.Message)"
                $failedGroups += $group
            }
        }
        
        # Summary
        Write-Log "Admin group configuration completed:"
        Write-Log "  Added: $($addedGroups.Count) groups ($($addedGroups -join ', '))"
        Write-Log "  Skipped: $($skippedGroups.Count) groups ($($skippedGroups -join ', '))"
        Write-Log "  Failed: $($failedGroups.Count) groups ($($failedGroups -join ', '))"
        
        # Return result object
        return @{
            Success = ($failedGroups.Count -eq 0)
            Added = $addedGroups
            Skipped = $skippedGroups
            Failed = $failedGroups
            Message = "Added $($addedGroups.Count) groups, skipped $($skippedGroups.Count), failed $($failedGroups.Count)"
        }
        
    } catch {
        $errorMsg = "Error in Add-AdminGroupsToLocal: $($_.Exception.Message)"
        Write-Log $errorMsg
        return @{
            Success = $false
            Added = @()
            Skipped = @()
            Failed = $AdminGroups
            Message = $errorMsg
        }
    }
}

# Example usage function for user-data scripts
function Set-DefaultAdminGroups {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory=$true)]
        [string]$ConfigPath,
        
        [Parameter(Mandatory=$true)]
        [string]$BusinessUnit,
        
        [Parameter(Mandatory=$true)]
        [string]$Environment,
        
        [Parameter(Mandatory=$false)]
        [string]$LogPath = "C:\Scripts\admin-groups.log"
    )
    
    try {
        # Get admin groups from configuration
        $adminGroups = Get-AdminGroups -ConfigPath $ConfigPath -BusinessUnit $BusinessUnit -Environment $Environment
        
        if (!$adminGroups -or $adminGroups.Count -eq 0) {
            Write-Warning "No admin groups found for $BusinessUnit/$Environment"
            return $false
        }
        
        # Add groups to local administrators
        $result = Add-AdminGroupsToLocal -AdminGroups $adminGroups -LogPath $LogPath
        
        return $result.Success
        
    } catch {
        Write-Error "Error in Set-DefaultAdminGroups: $($_.Exception.Message)"
        return $false
    }
}

# Export functions for use in other scripts
Export-ModuleMember -Function Get-AdminGroups, Add-AdminGroupsToLocal, Set-DefaultAdminGroups
