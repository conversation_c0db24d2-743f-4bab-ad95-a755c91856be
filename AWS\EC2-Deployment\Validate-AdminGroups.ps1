# Validate-AdminGroups.ps1
# Script to validate that the correct admin groups are configured on the server

[CmdletBinding()]
param(
    [Parameter(Mandatory=$false)]
    [string]$ConfigPath = "C:\Scripts\SLM_Config.json",
    
    [Parameter(Mandatory=$false)]
    [string]$BusinessUnit,
    
    [Parameter(Mandatory=$false)]
    [string]$Environment,
    
    [Parameter(Mandatory=$false)]
    [switch]$Detailed,
    
    [Parameter(Mandatory=$false)]
    [string]$OutputPath
)

function Write-ValidationLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    Write-Host $logEntry
    
    if ($OutputPath) {
        $logEntry | Out-File -FilePath $OutputPath -Append -Encoding UTF8
    }
}

function Get-ExpectedAdminGroups {
    param($ConfigPath, $BusinessUnit, $Environment)
    
    try {
        if (!(Test-Path $ConfigPath)) {
            Write-ValidationLog "Configuration file not found: $ConfigPath" "WARNING"
            return $null
        }
        
        $config = Get-Content $ConfigPath -Raw | ConvertFrom-Json
        $buConfig = $config.business_units.$BusinessUnit
        
        if (!$buConfig) {
            Write-ValidationLog "Business unit '$BusinessUnit' not found in configuration" "WARNING"
            return $null
        }
        
        $envConfig = $buConfig.environments.$Environment
        if (!$envConfig) {
            Write-ValidationLog "Environment '$Environment' not found for business unit '$BusinessUnit'" "WARNING"
            return $null
        }
        
        return $envConfig.DEFAULT_ADM
        
    } catch {
        Write-ValidationLog "Error reading configuration: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Get-CurrentAdminGroups {
    try {
        $admins = Get-LocalGroupMember -Group "Administrators" -ErrorAction Stop
        $adGroups = $admins | Where-Object { $_.PrincipalSource -eq "ActiveDirectory" } | Select-Object -ExpandProperty Name
        return $adGroups
    } catch {
        Write-ValidationLog "Error retrieving current administrators: $($_.Exception.Message)" "ERROR"
        return @()
    }
}

function Test-AdminGroupsConfiguration {
    param($ExpectedGroups, $CurrentGroups)
    
    $result = @{
        IsValid = $true
        MissingGroups = @()
        ExtraGroups = @()
        PresentGroups = @()
        Summary = ""
    }
    
    if (!$ExpectedGroups -or $ExpectedGroups.Count -eq 0) {
        $result.IsValid = $false
        $result.Summary = "No expected admin groups defined"
        return $result
    }
    
    # Check for missing groups
    foreach ($expectedGroup in $ExpectedGroups) {
        if ($CurrentGroups -contains $expectedGroup) {
            $result.PresentGroups += $expectedGroup
        } else {
            $result.MissingGroups += $expectedGroup
            $result.IsValid = $false
        }
    }
    
    # Check for extra groups (optional - might be legitimate)
    foreach ($currentGroup in $CurrentGroups) {
        if ($ExpectedGroups -notcontains $currentGroup) {
            $result.ExtraGroups += $currentGroup
        }
    }
    
    # Generate summary
    if ($result.IsValid) {
        $result.Summary = "All expected admin groups are present"
    } else {
        $result.Summary = "Missing $($result.MissingGroups.Count) expected admin groups"
    }
    
    return $result
}

# Main execution
try {
    Write-ValidationLog "Starting admin groups validation..."
    
    # Try to determine business unit and environment if not provided
    if (!$BusinessUnit -or !$Environment) {
        Write-ValidationLog "Business unit or environment not specified, attempting to detect from business config..."
        
        $businessConfigPath = "C:\Scripts\business-config.json"
        if (Test-Path $businessConfigPath) {
            try {
                $businessConfig = Get-Content $businessConfigPath -Raw | ConvertFrom-Json
                if (!$BusinessUnit) { $BusinessUnit = $businessConfig.business_unit }
                if (!$Environment) { $Environment = $businessConfig.environment }
                Write-ValidationLog "Detected: Business Unit = $BusinessUnit, Environment = $Environment"
            } catch {
                Write-ValidationLog "Failed to read business config: $($_.Exception.Message)" "WARNING"
            }
        }
    }
    
    # Fallback detection methods
    if (!$Environment) {
        # Try to detect from domain
        try {
            $domain = (Get-WmiObject -Class Win32_ComputerSystem).Domain
            if ($domain -like "*dev*") { $Environment = "DEV" }
            elseif ($domain -like "*ppe*") { $Environment = "PPE" }
            else { $Environment = "PRD" }
            Write-ValidationLog "Detected environment from domain: $Environment"
        } catch {
            Write-ValidationLog "Could not detect environment from domain" "WARNING"
        }
    }
    
    if (!$BusinessUnit) {
        $BusinessUnit = "SPF"  # Default assumption
        Write-ValidationLog "Using default business unit: $BusinessUnit" "WARNING"
    }
    
    # Get expected admin groups
    Write-ValidationLog "Getting expected admin groups for $BusinessUnit/$Environment..."
    $expectedGroups = Get-ExpectedAdminGroups -ConfigPath $ConfigPath -BusinessUnit $BusinessUnit -Environment $Environment
    
    if (!$expectedGroups) {
        Write-ValidationLog "Could not determine expected admin groups from configuration" "ERROR"
        Write-ValidationLog "Please ensure SLM_Config.json is available and contains DEFAULT_ADM for $BusinessUnit/$Environment" "ERROR"
        Write-ValidationLog "Validation cannot proceed without expected admin groups configuration" "ERROR"
        exit 3
    }
    
    Write-ValidationLog "Expected admin groups: $($expectedGroups -join ', ')"
    
    # Get current admin groups
    Write-ValidationLog "Getting current AD admin groups..."
    $currentGroups = Get-CurrentAdminGroups
    Write-ValidationLog "Current AD admin groups: $($currentGroups -join ', ')"
    
    # Validate configuration
    Write-ValidationLog "Validating admin groups configuration..."
    $validation = Test-AdminGroupsConfiguration -ExpectedGroups $expectedGroups -CurrentGroups $currentGroups
    
    # Output results
    Write-ValidationLog "=== VALIDATION RESULTS ===" "INFO"
    Write-ValidationLog "Status: $(if ($validation.IsValid) { 'PASS' } else { 'FAIL' })" "INFO"
    Write-ValidationLog "Summary: $($validation.Summary)" "INFO"
    
    if ($validation.PresentGroups.Count -gt 0) {
        Write-ValidationLog "Present groups ($($validation.PresentGroups.Count)): $($validation.PresentGroups -join ', ')" "INFO"
    }
    
    if ($validation.MissingGroups.Count -gt 0) {
        Write-ValidationLog "Missing groups ($($validation.MissingGroups.Count)): $($validation.MissingGroups -join ', ')" "ERROR"
    }
    
    if ($validation.ExtraGroups.Count -gt 0 -and $Detailed) {
        Write-ValidationLog "Extra groups ($($validation.ExtraGroups.Count)): $($validation.ExtraGroups -join ', ')" "INFO"
    }
    
    # Detailed output
    if ($Detailed) {
        Write-ValidationLog "=== DETAILED INFORMATION ===" "INFO"
        Write-ValidationLog "Business Unit: $BusinessUnit" "INFO"
        Write-ValidationLog "Environment: $Environment" "INFO"
        Write-ValidationLog "Config Path: $ConfigPath" "INFO"
        Write-ValidationLog "Config Exists: $(Test-Path $ConfigPath)" "INFO"
        
        # Show all local administrators
        try {
            $allAdmins = Get-LocalGroupMember -Group "Administrators" | Select-Object Name, ObjectClass, PrincipalSource
            Write-ValidationLog "All local administrators:" "INFO"
            foreach ($admin in $allAdmins) {
                Write-ValidationLog "  $($admin.Name) ($($admin.ObjectClass), $($admin.PrincipalSource))" "INFO"
            }
        } catch {
            Write-ValidationLog "Could not retrieve all administrators: $($_.Exception.Message)" "WARNING"
        }
    }
    
    # Return exit code
    if ($validation.IsValid) {
        Write-ValidationLog "Admin groups validation completed successfully" "INFO"
        exit 0
    } else {
        Write-ValidationLog "Admin groups validation failed" "ERROR"
        exit 1
    }
    
} catch {
    Write-ValidationLog "Validation script failed: $($_.Exception.Message)" "ERROR"
    Write-ValidationLog "Stack trace: $($_.ScriptStackTrace)" "ERROR"
    exit 2
}
