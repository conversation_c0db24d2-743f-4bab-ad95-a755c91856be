# BGInfo Environment-Specific Configuration

This document explains how to use the BGInfo component with environment-specific background colors for DEV, PPE, and PROD environments.

## Environment Configurations

The BGInfo component supports three predefined environments with distinct background colors:

### DEV (Development)
- **Background Color**: Dark Green
- **RGB Values**: Red: 0, Green: 128, Blue: 0
- **HSL Values**: Hue: 80, Sat: 240, Lum: 60
- **Environment Label**: "Development (DEV)"

### PPE (Pre-Production)
- **Background Color**: Yellow/Gold
- **RGB Values**: Red: 228, Green: 199, Blue: 27
- **HSL Values**: Hue: 34, Sat: 189, Lum: 120
- **Environment Label**: "Pre-Production (PPE)"

### PROD (Production)
- **Background Color**: Red
- **RGB Values**: Red: 255, Green: 0, Blue: 0
- **HSL Values**: Hue: 0, Sat: 240, Lum: 120
- **Environment Label**: "Production (PROD)"

## Usage

### Setting the Environment

Set the `BGINFO_ENVIRONMENT` environment variable to specify which configuration to use:

```powershell
# For Development
$env:BGINFO_ENVIRONMENT = "DEV"

# For Pre-Production
$env:BGINFO_ENVIRONMENT = "PPE"

# For Production
$env:BGINFO_ENVIRONMENT = "PROD"
```

### In AWS Systems Manager Parameter Store

You can set this as a parameter in Systems Manager:

```bash
# Create parameter for DEV environment
aws ssm put-parameter \
    --name "/bginfo/environment" \
    --value "DEV" \
    --type "String" \
    --description "BGInfo environment configuration"

# For PPE
aws ssm put-parameter \
    --name "/bginfo/environment" \
    --value "PPE" \
    --type "String" \
    --overwrite

# For PROD
aws ssm put-parameter \
    --name "/bginfo/environment" \
    --value "PROD" \
    --type "String" \
    --overwrite
```

### In ImageBuilder Recipe

Add the environment variable to your ImageBuilder recipe:

```yaml
# In your recipe YAML
components:
- componentArn: "arn:aws:imagebuilder:region:account:component/win-server-bginfo/1.0.0"
  parameters:
  - name: "BGINFO_ENVIRONMENT"
    value: "PROD"  # or "DEV" or "PPE"
```

### Default Behavior

If no `BGINFO_ENVIRONMENT` is specified, the component defaults to **DEV** configuration.

## Generated Files

The component creates the following files:

1. **Environment-specific config**: `C:\Program Files\BGInfo\bginfo-{env}.bgi`
   - `bginfo-dev.bgi` for DEV
   - `bginfo-ppe.bgi` for PPE
   - `bginfo-prod.bgi` for PROD

2. **Default config**: `C:\Program Files\BGInfo\bginfo.bgi` (copy of environment-specific)

## Information Displayed

Each configuration displays the following system information:

- **Computer Name** and Description (header in green)
- **Environment** (clearly labeled with environment name)
- **User Information**: Domain\Username, Machine Domain
- **System Information**: OS Version, Service Pack, Boot Time
- **Hardware**: CPU, Memory
- **Storage**: Volumes, Free Space
- **Network**: IP Address, Subnet Mask, Default Gateway, DHCP Server, DNS Server
- **Domain**: Logon Server

## Automatic Startup

BGInfo is configured to start automatically:

1. **Registry Entry**: Added to `HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Run`
2. **Scheduled Task**: Created for system startup
3. **Silent Execution**: Runs without user prompts (`/timer:0 /nolicprompt /silent`)

## Customization

To modify the displayed information or formatting:

1. Edit the `$configLines` array in the component
2. Modify the RTF formatting codes
3. Add or remove BGInfo field placeholders (e.g., `<Computer Name>`, `<IP Address>`)

## Color Calculation

Background colors are calculated using the formula:
```
DesktopColor = Red + (Green * 256) + (Blue * 65536)
```

Examples:
- DEV: 0 + (128 * 256) + (0 * 65536) = 32768
- PPE: 228 + (199 * 256) + (27 * 65536) = 1819876
- PROD: 255 + (0 * 256) + (0 * 65536) = 255

## Troubleshooting

### Environment Not Applied
- Check that `BGINFO_ENVIRONMENT` is set correctly
- Verify the environment-specific config file exists
- Check Windows Event Logs for BGInfo errors

### BGInfo Not Starting
- Verify registry entry exists
- Check scheduled task is created and enabled
- Ensure BGInfo executable has proper permissions

### Wrong Background Color
- Confirm the correct environment-specific config is being used
- Check the DesktopColor value in the .bgi file
- Restart the system or re-run BGInfo manually

## Manual Testing

To test BGInfo manually:

```powershell
# Test DEV configuration
& "C:\Program Files\BGInfo\Bginfo64.exe" "C:\Program Files\BGInfo\bginfo-dev.bgi" /timer:0 /nolicprompt

# Test PPE configuration
& "C:\Program Files\BGInfo\Bginfo64.exe" "C:\Program Files\BGInfo\bginfo-ppe.bgi" /timer:0 /nolicprompt

# Test PROD configuration
& "C:\Program Files\BGInfo\Bginfo64.exe" "C:\Program Files\BGInfo\bginfo-prod.bgi" /timer:0 /nolicprompt
```
