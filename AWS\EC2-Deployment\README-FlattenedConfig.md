# Flattened SLM_Config.json Structure

The SLM_Config.json has been flattened to use the Business Unit (BU) as a direct key instead of nesting under "business_units".

## Structure Comparison

### Before (Nested)
```json
{
  "CLIENT": "Sanlam",
  "business_units": {
    "SPF": {
      "BU": "SPF",
      "environments": { ... }
    }
  }
}
```

### After (Flattened)
```json
{
  "CLIENT": "Sanlam",
  "SPF": {
    "BU": "SPF", 
    "environments": { ... }
  },
  "SC": {
    "BU": "SC",
    "environments": { ... }
  }
}
```

## Tag Mapping

| EC2 Tag | Config Path | Example |
|---------|-------------|---------|
| `Business-Unit` | `{BU}` | `SPF` → `config.SPF` |
| `Environment` | `{BU}.environments.{ENV}` | `PRD` → `config.SPF.environments.PRD` |
| `Server-Role` | `{BU}.environments.{ENV}.APP_TYPE.{TYPE}` | `MSSQL` → `config.SPF.environments.PRD.APP_TYPE.MSSQL` |

## Script Changes

### EC2 Deployment Script
```powershell
# Before (nested)
$buConfig = $slmConfig.business_units.$businessUnit

# After (flattened)  
$buConfig = $slmConfig.$businessUnit
```

### Validation
```powershell
# Before (nested)
$availableBUs = $slmConfig.business_units.PSObject.Properties.Name

# After (flattened)
$availableBUs = $slmConfig.PSObject.Properties.Name | Where-Object { $_ -ne "CLIENT" }
```

## Benefits

1. **Simpler Navigation**: Direct access to business units
2. **Flatter Structure**: Less nesting levels
3. **Cleaner Code**: Fewer property traversals
4. **Better Performance**: Faster JSON parsing

## Example Usage

```powershell
# Load config
$config = Get-Content "SLM_Config.json" | ConvertFrom-Json

# Access SPF PRD admin groups directly
$adminGroups = $config.SPF.environments.PRD.DEFAULT_ADM

# Access SC DEV OU path
$ouPath = $config.SC.environments.DEV.APP_TYPE.Shared
```
